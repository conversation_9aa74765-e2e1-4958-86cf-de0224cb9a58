'use client';

import { useState, useEffect } from 'react';
import { api } from '../lib/api';

export default function AuthorFilter({ 
  selectedAuthor, 
  onAuthorChange, 
  className = '' 
}) {
  const [authors, setAuthors] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchAuthors = async () => {
      try {
        const response = await api.authors.getAll({ pageSize: 100 });
        setAuthors(response.results || []);
      } catch (error) {
        console.error('Error fetching authors:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchAuthors();
  }, []);

  if (loading) {
    return (
      <div className={`${className}`}>
        <select disabled className="w-full px-3 py-2 border border-secondary-dark rounded-lg bg-background">
          <option>Loading authors...</option>
        </select>
      </div>
    );
  }

  return (
    <div className={`${className}`}>
      <select
        value={selectedAuthor || ''}
        onChange={(e) => onAuthorChange(e.target.value || null)}
        className="w-full px-3 py-2 border border-secondary-dark rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent bg-background"
      >
        <option value="">All Authors</option>
        {authors.map((author) => (
          <option key={author.id} value={author.id}>
            {author.name}
          </option>
        ))}
      </select>
    </div>
  );
}
