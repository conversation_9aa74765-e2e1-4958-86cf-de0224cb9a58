import Link from 'next/link';
import Image from 'next/image';

export default function ArticleCard({ article }) {
  // Handle both API data structure and legacy props
  const {
    id,
    slug,
    title,
    content,
    short_description,
    categories = [],
    authors = [],
    status,
    created_at,
    image,
    featured_image,
    original_article_url,
    // Legacy props for backward compatibility
    excerpt,
    category,
    author,
    date,
  } = article || {};

  // Extract data with fallbacks
  const articleSlug = slug || 'article-slug';
  const articleTitle = title || 'Finding Strength in Biblical Principles';
  const articleExcerpt = short_description || excerpt || (content ? content.replace(/<[^>]*>/g, '').substring(0, 150) + '...' : 'Discover how ancient wisdom can guide modern women through today\'s challenges and opportunities.');
  const articleImage = featured_image || image;

  // Handle categories (API returns array, legacy might be string)
  const primaryCategory = categories.length > 0 ? categories[0] : (category ? { name: category, slug: category.toLowerCase().replace(/\s+/g, '-') } : { name: 'Biblical Women', slug: 'biblical-women' });

  // Handle authors (API returns array, legacy might be object)
  const primaryAuthor = authors.length > 0 ? authors[0] : (author || { name: '<PERSON>', image: null });

  // Handle date formatting
  const displayDate = created_at ? new Date(created_at).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  }) : (date || 'May 15, 2023');

  return (
    <div className="bg-background rounded-lg overflow-hidden border border-secondary-dark hover:border-primary-light transition-colors">
      <Link href={`/articles/${articleSlug}`}>
        <div className="h-48 bg-secondary relative">
          {articleImage ? (
            <img
              src={articleImage}
              alt={articleTitle}
              className="w-full h-full object-cover"
            />
          ) : (
            <div className="absolute inset-0 flex items-center justify-center text-primary">
              Article Image
            </div>
          )}
        </div>
      </Link>
      <div className="p-6">
        {/* Categories */}
        <div className="flex flex-wrap gap-2 mb-2">
          {categories.length > 0 ? (
            categories.slice(0, 2).map((cat) => (
              <Link key={cat.slug} href={`/categories/${cat.slug}`}>
                <span className="text-sm text-primary-dark font-medium hover:text-primary">
                  {cat.name}
                </span>
              </Link>
            ))
          ) : (
            <Link href={`/categories/${primaryCategory.slug}`}>
              <span className="text-sm text-primary-dark font-medium hover:text-primary">
                {primaryCategory.name}
              </span>
            </Link>
          )}
        </div>

        <Link href={`/articles/${articleSlug}`}>
          <h3 className="text-xl mb-2 hover:text-primary transition-colors">{articleTitle}</h3>
        </Link>
        <p className="text-primary-dark mb-4 line-clamp-2">{articleExcerpt}</p>

        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <div className="w-10 h-10 rounded-full bg-secondary mr-3 relative overflow-hidden">
              {primaryAuthor.image ? (
                <img
                  src={primaryAuthor.image}
                  alt={primaryAuthor.name}
                  className="w-full h-full object-cover"
                />
              ) : (
                <div className="absolute inset-0 flex items-center justify-center text-primary text-sm font-semibold">
                  {primaryAuthor.name.charAt(0)}
                </div>
              )}
            </div>
            <div>
              <div className="font-medium">{primaryAuthor.name}</div>
              <div className="text-sm text-primary-dark">{displayDate}</div>
            </div>
          </div>
          {original_article_url && (
            <a
              href={original_article_url}
              target="_blank"
              rel="noopener noreferrer"
              className="text-xs text-primary hover:text-primary-dark transition-colors"
              title="View original article"
            >
              Original
            </a>
          )}
        </div>
      </div>
    </div>
  );
}
