import Image from 'next/image';
import Link from 'next/link';

export default function TestimonialCard({ testimonial }) {
  // Handle both API data structure and legacy props
  const {
    id,
    name,
    email,
    content,
    image,
    status,
    created_at,
    // Legacy props for backward compatibility
    quote,
    author,
  } = testimonial || {};

  // Extract data with fallbacks
  const testimonialContent = content || quote || 'The wisdom I\'ve found through this community has transformed my approach to balancing my career, family, and faith. I\'m grateful for the practical guidance rooted in biblical principles.';
  const testimonialAuthor = {
    name: name || author?.name || '<PERSON>',
    title: author?.title || 'Community Member',
    image: image || author?.image || null,
  };

  const displayDate = created_at ? new Date(created_at).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  }) : null;

  return (
    <div className="bg-background p-8 rounded-lg border border-secondary-dark">
      <div className="text-accent text-5xl font-serif mb-4">"</div>
      <p className="text-lg italic mb-6 text-primary-dark">{testimonialContent}</p>
      <div className="flex items-center justify-between">
        <div className="flex items-center">
          <div className="w-12 h-12 rounded-full bg-secondary mr-4 relative overflow-hidden">
            {testimonialAuthor.image ? (
              <img
                src={testimonialAuthor.image}
                alt={testimonialAuthor.name}
                className="w-full h-full object-cover"
              />
            ) : (
              <div className="absolute inset-0 flex items-center justify-center text-primary text-lg font-semibold">
                {testimonialAuthor.name.charAt(0)}
              </div>
            )}
          </div>
          <div>
            <div className="font-medium">{testimonialAuthor.name}</div>
            <div className="text-sm text-primary-dark">{testimonialAuthor.title}</div>
          </div>
        </div>

        {displayDate && (
          <div className="text-sm text-primary-dark">
            {displayDate}
          </div>
        )}
      </div>

      {id && (
        <div className="mt-4 pt-4 border-t border-secondary-dark">
          <Link
            href={`/testimonies/${id}`}
            className="text-primary text-sm font-medium hover:text-primary-dark"
          >
            Soma ubuhamya bwose
          </Link>
        </div>
      )}
    </div>
  );
}
