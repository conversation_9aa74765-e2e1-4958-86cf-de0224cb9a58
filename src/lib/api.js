import {
  API_BASE_URL,
  API_ENDPOINTS,
  DEFAULT_PAGE_SIZE,
  STATUS_OPTIONS
} from './constants';
import cache, { cacheKeys } from './cache';

// Generic API fetch function with retry logic
async function apiFetch(endpoint, options = {}, retries = 3) {
  const url = `${API_BASE_URL}${endpoint}`;

  for (let attempt = 1; attempt <= retries; attempt++) {
    try {
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 15000); // 15 second timeout

      const response = await fetch(url, {
        headers: {
          'Content-Type': 'application/json',
          ...options.headers,
        },
        signal: controller.signal,
        ...options,
      });

      clearTimeout(timeoutId);

      if (!response.ok) {
        throw new Error(`API Error: ${response.status} ${response.statusText}`);
      }

      return await response.json();
    } catch (error) {
      console.error(`API Fetch Error (attempt ${attempt}/${retries}):`, error);

      if (attempt === retries) {
        // If this is the last attempt, throw the error
        if (error.name === 'AbortError') {
          throw new Error('Request timed out. Please check your internet connection.');
        }
        throw error;
      }

      // Wait before retrying (exponential backoff)
      await new Promise(resolve => setTimeout(resolve, Math.pow(2, attempt) * 1000));
    }
  }
}

// Build query string from parameters
function buildQueryString(params) {
  const searchParams = new URLSearchParams();

  Object.entries(params).forEach(([key, value]) => {
    if (value !== undefined && value !== null && value !== '') {
      searchParams.append(key, value);
    }
  });

  return searchParams.toString();
}

// Categories API
export const categoriesApi = {
  // Get all categories
  getAll: async (params = {}) => {
    const cacheKey = cacheKeys.categories(params);
    const cached = cache.get(cacheKey);

    if (cached) {
      return cached;
    }

    const queryString = buildQueryString({
      page: params.page || 1,
      page_size: params.pageSize || DEFAULT_PAGE_SIZE,
      search: params.search,
      ordering: params.ordering,
    });

    const endpoint = `${API_ENDPOINTS.CATEGORIES}${queryString ? `?${queryString}` : ''}`;
    const result = await apiFetch(endpoint);

    // Cache for 10 minutes (categories don't change often)
    cache.set(cacheKey, result, 600000);
    return result;
  },

  // Get category by slug
  getBySlug: async (slug) => {
    const cacheKey = cacheKeys.category(slug);
    const cached = cache.get(cacheKey);

    if (cached) {
      return cached;
    }

    const result = await apiFetch(API_ENDPOINTS.CATEGORY_DETAIL(slug));

    // Cache for 10 minutes
    cache.set(cacheKey, result, 600000);
    return result;
  },


};

// Articles API
export const articlesApi = {
  // Get all articles
  getAll: async (params = {}) => {
    const cacheKey = cacheKeys.articles(params);
    const cached = cache.get(cacheKey);

    if (cached) {
      return cached;
    }

    const queryString = buildQueryString({
      page: params.page || 1,
      page_size: params.pageSize || DEFAULT_PAGE_SIZE,
      categories: params.categories, // comma-separated slugs
      categories__name: params.categoryName, // filter by category name
      authors__name: params.authorName, // filter by author name
      status: params.status || STATUS_OPTIONS.PUBLISHED,
      search: params.search,
      ordering: params.ordering || '-created_at',
    });

    const endpoint = `${API_ENDPOINTS.ARTICLES}${queryString ? `?${queryString}` : ''}`;
    const result = await apiFetch(endpoint);

    // Cache for 5 minutes (articles change more frequently)
    cache.set(cacheKey, result, 300000);
    return result;
  },

  // Get article by slug
  getBySlug: async (slug) => {
    const cacheKey = cacheKeys.article(slug);
    const cached = cache.get(cacheKey);

    if (cached) {
      return cached;
    }

    const result = await apiFetch(API_ENDPOINTS.ARTICLE_DETAIL(slug));

    // Cache for 10 minutes
    cache.set(cacheKey, result, 600000);
    return result;
  },


};

// Testimonies API
export const testimoniesApi = {
  // Get all testimonies
  getAll: async (params = {}) => {
    const cacheKey = cacheKeys.testimonies(params);
    const cached = cache.get(cacheKey);

    if (cached) {
      return cached;
    }

    const queryString = buildQueryString({
      page: params.page || 1,
      page_size: params.pageSize || DEFAULT_PAGE_SIZE,
      status: params.status || STATUS_OPTIONS.PUBLISHED,
      ordering: params.ordering || '-created_at',
    });

    const endpoint = `${API_ENDPOINTS.TESTIMONIES}${queryString ? `?${queryString}` : ''}`;
    const result = await apiFetch(endpoint);

    // Cache for 5 minutes
    cache.set(cacheKey, result, 300000);
    return result;
  },

  // Get testimony by ID
  getById: async (id) => {
    const cacheKey = cacheKeys.testimony(id);
    const cached = cache.get(cacheKey);

    if (cached) {
      return cached;
    }

    const result = await apiFetch(API_ENDPOINTS.TESTIMONY_DETAIL(id));

    // Cache for 10 minutes
    cache.set(cacheKey, result, 600000);
    return result;
  },
};

// Partners API
export const partnersApi = {
  // Get all partners
  getAll: async (params = {}) => {
    const cacheKey = cacheKeys.partners(params);
    const cached = cache.get(cacheKey);

    if (cached) {
      return cached;
    }

    const queryString = buildQueryString({
      page: params.page || 1,
      page_size: params.pageSize || DEFAULT_PAGE_SIZE,
      status: params.status || STATUS_OPTIONS.PUBLISHED,
      categories: params.categories,
    });

    const endpoint = `${API_ENDPOINTS.PARTNERS}${queryString ? `?${queryString}` : ''}`;
    const result = await apiFetch(endpoint);

    // Cache for 10 minutes
    cache.set(cacheKey, result, 600000);
    return result;
  },

  // Get partner by slug
  getBySlug: async (slug) => {
    const cacheKey = cacheKeys.partner(slug);
    const cached = cache.get(cacheKey);

    if (cached) {
      return cached;
    }

    const result = await apiFetch(API_ENDPOINTS.PARTNER_DETAIL(slug));

    // Cache for 10 minutes
    cache.set(cacheKey, result, 600000);
    return result;
  },
};

// Authors API
export const authorsApi = {
  // Get all authors
  getAll: async (params = {}) => {
    const cacheKey = cacheKeys.authors(params);
    const cached = cache.get(cacheKey);

    if (cached) {
      return cached;
    }

    const queryString = buildQueryString({
      page: params.page || 1,
      page_size: params.pageSize || DEFAULT_PAGE_SIZE,
    });

    const endpoint = `${API_ENDPOINTS.AUTHORS}${queryString ? `?${queryString}` : ''}`;
    const result = await apiFetch(endpoint);

    // Cache for 10 minutes
    cache.set(cacheKey, result, 600000);
    return result;
  },

  // Get author by ID
  getById: async (id) => {
    const cacheKey = cacheKeys.author(id);
    const cached = cache.get(cacheKey);

    if (cached) {
      return cached;
    }

    const result = await apiFetch(API_ENDPOINTS.AUTHOR_DETAIL(id));

    // Cache for 10 minutes
    cache.set(cacheKey, result, 600000);
    return result;
  },
};

// Combined API object
export const api = {
  categories: categoriesApi,
  articles: articlesApi,
  testimonies: testimoniesApi,
  partners: partnersApi,
  authors: authorsApi,
};
