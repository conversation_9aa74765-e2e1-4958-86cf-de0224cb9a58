// API Configuration
export const API_BASE_URL = 'https://umugore-uzashimwa-backend.onrender.com/api';

// Site Configuration
export const SITE_CONFIG = {
  name: '<PERSON>ug<PERSON>',
  url: 'https://umugoreuzashimwa.org',
  description: 'A platform for women seeking inspiration, practical advice, and community support through a blend of modern womanhood and biblical principles.',
  defaultImage: '/og-image.jpg', // TODO: Add a default Open Graph image (1200x630px)
};

// API Endpoints
export const API_ENDPOINTS = {
  // Categories
  CATEGORIES: '/categories/',
  CATEGORY_DETAIL: (slug) => `/categories/${slug}/`,
  CATEGORY_ARTICLES: (slug) => `/category/${slug}/`,

  // Articles
  ARTICLES: '/articles/',
  ARTICLE_DETAIL: (slug) => `/articles/${slug}/`,
  ARTICLES_BY_AUTHOR: (authorId) => `/articles/author/${authorId}/`,

  // Testimonies
  TESTIMONIES: '/testimonies/',
  TESTIMONY_DETAIL: (id) => `/testimonies/${id}/`,

  // Partners
  PARTNERS: '/partners/',
  PARTNER_DETAIL: (slug) => `/partners/${slug}/`,

  // Authors
  AUTHORS: '/accounts/authors/',
  AUTHOR_DETAIL: (id) => `/accounts/authors/${id}/`,
};

// Default pagination settings
export const DEFAULT_PAGE_SIZE = 10;
export const MAX_PAGE_SIZE = 100;

// Status options
export const STATUS_OPTIONS = {
  PUBLISHED: 'published',
  DRAFT: 'draft',
};

// Ordering options
export const ORDERING_OPTIONS = {
  NEWEST_FIRST: '-created_at',
  OLDEST_FIRST: 'created_at',
  TITLE_ASC: 'title',
  TITLE_DESC: '-title',
  NAME_ASC: 'name',
  NAME_DESC: '-name',
};
