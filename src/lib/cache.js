// Simple in-memory cache with TTL (Time To Live)
class Cache {
  constructor() {
    this.cache = new Map();
    this.timers = new Map();
  }

  set(key, value, ttl = 300000) { // Default 5 minutes TTL
    // Clear existing timer if any
    if (this.timers.has(key)) {
      clearTimeout(this.timers.get(key));
    }

    // Set the value
    this.cache.set(key, {
      value,
      timestamp: Date.now(),
      ttl
    });

    // Set expiration timer
    const timer = setTimeout(() => {
      this.delete(key);
    }, ttl);

    this.timers.set(key, timer);
  }

  get(key) {
    const item = this.cache.get(key);
    
    if (!item) {
      return null;
    }

    // Check if expired
    if (Date.now() - item.timestamp > item.ttl) {
      this.delete(key);
      return null;
    }

    return item.value;
  }

  delete(key) {
    // Clear timer
    if (this.timers.has(key)) {
      clearTimeout(this.timers.get(key));
      this.timers.delete(key);
    }

    // Remove from cache
    this.cache.delete(key);
  }

  clear() {
    // Clear all timers
    this.timers.forEach(timer => clearTimeout(timer));
    this.timers.clear();
    this.cache.clear();
  }

  has(key) {
    const item = this.cache.get(key);
    if (!item) return false;
    
    // Check if expired
    if (Date.now() - item.timestamp > item.ttl) {
      this.delete(key);
      return false;
    }
    
    return true;
  }

  size() {
    return this.cache.size;
  }
}

// Create a global cache instance
const cache = new Cache();

// Cache key generators
export const cacheKeys = {
  articles: (params = {}) => {
    const key = Object.keys(params)
      .sort()
      .map(k => `${k}:${params[k]}`)
      .join('|');
    return `articles:${key}`;
  },
  
  article: (slug) => `article:${slug}`,
  
  categories: (params = {}) => {
    const key = Object.keys(params)
      .sort()
      .map(k => `${k}:${params[k]}`)
      .join('|');
    return `categories:${key}`;
  },
  
  category: (slug) => `category:${slug}`,

  categoryArticles: (slug, params = {}) => {
    const key = Object.keys(params)
      .sort()
      .map(k => `${k}:${params[k]}`)
      .join('|');
    return `category-articles:${slug}:${key}`;
  },

  articlesByAuthor: (authorId, params = {}) => {
    const key = Object.keys(params)
      .sort()
      .map(k => `${k}:${params[k]}`)
      .join('|');
    return `articles-by-author:${authorId}:${key}`;
  },

  testimonies: (params = {}) => {
    const key = Object.keys(params)
      .sort()
      .map(k => `${k}:${params[k]}`)
      .join('|');
    return `testimonies:${key}`;
  },
  
  testimony: (id) => `testimony:${id}`,
  
  partners: (params = {}) => {
    const key = Object.keys(params)
      .sort()
      .map(k => `${k}:${params[k]}`)
      .join('|');
    return `partners:${key}`;
  },
  
  partner: (slug) => `partner:${slug}`,
  
  authors: (params = {}) => {
    const key = Object.keys(params)
      .sort()
      .map(k => `${k}:${params[k]}`)
      .join('|');
    return `authors:${key}`;
  },
  
  author: (id) => `author:${id}`,
  
  homepage: () => 'homepage:data'
};

export default cache;
