'use client';

import { useState, useEffect, Suspense } from 'react';
import { useSearchParams } from 'next/navigation';
import Header from '../../components/Header';
import Footer from '../../components/Footer';
import ArticleCard from '../../components/ArticleCard';
import SearchBar from '../../components/SearchBar';
import CategoryFilter from '../../components/CategoryFilter';
import AuthorFilter from '../../components/AuthorFilter';
import Pagination from '../../components/Pagination';
import LoadingSpinner from '../../components/LoadingSpinner';
import ErrorMessage from '../../components/ErrorMessage';
import { ArticleGridSkeleton } from '../../components/SkeletonCard';
import { api } from '../../lib/api';
import { ORDERING_OPTIONS } from '../../lib/constants';

// Component that handles search params (needs to be in Suspense)
function ArticlesContent() {
  const searchParams = useSearchParams();

  // State management
  const [articles, setArticles] = useState([]);
  const [categories, setCategories] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategories, setSelectedCategories] = useState([]);
  const [selectedAuthor, setSelectedAuthor] = useState(null);
  const [ordering, setOrdering] = useState(ORDERING_OPTIONS.NEWEST_FIRST);

  // Initialize search term from URL parameters
  useEffect(() => {
    const urlSearchTerm = searchParams.get('search');
    if (urlSearchTerm) {
      setSearchTerm(urlSearchTerm);
    }
  }, [searchParams]);

  // Fetch articles based on current filters
  const fetchArticles = async () => {
    setLoading(true);
    setError(null);

    try {
      let response;

      if (selectedAuthor) {
        // Fetch articles by specific author
        const params = {
          page: currentPage,
          pageSize: 9,
          search: searchTerm,
          ordering: ordering,
        };
        response = await api.articles.getByAuthor(selectedAuthor, params);
      } else {
        // Fetch all articles with filters
        const params = {
          page: currentPage,
          pageSize: 9,
          search: searchTerm,
          ordering: ordering,
        };

        if (selectedCategories.length > 0) {
          params.categories = selectedCategories.join(',');
        }

        response = await api.articles.getAll(params);
      }

      setArticles(response.results || []);
      setTotalPages(response.total_pages || 1);
    } catch (err) {
      setError(err.message);
      setArticles([]);
    } finally {
      setLoading(false);
    }
  };

  // Fetch categories
  const fetchCategories = async () => {
    try {
      const response = await api.categories.getAll({ pageSize: 100 });
      setCategories(response.results || []);
    } catch (err) {
      console.error('Error fetching categories:', err);
    }
  };

  // Effects
  useEffect(() => {
    fetchCategories();
  }, []);

  useEffect(() => {
    fetchArticles();
  }, [currentPage, searchTerm, selectedCategories, selectedAuthor, ordering]);

  // Event handlers
  const handleSearch = (term) => {
    setSearchTerm(term);
    setCurrentPage(1);
  };

  const handleCategoryChange = (categoryList) => {
    setSelectedCategories(categoryList);
    setSelectedAuthor(null); // Clear author filter when category changes
    setCurrentPage(1);
  };

  const handleAuthorChange = (authorId) => {
    setSelectedAuthor(authorId);
    setSelectedCategories([]); // Clear category filter when author changes
    setCurrentPage(1);
  };

  const handlePageChange = (page) => {
    setCurrentPage(page);
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  const handleOrderingChange = (newOrdering) => {
    setOrdering(newOrdering);
    setCurrentPage(1);
  };

  return (
    <div className="min-h-screen flex flex-col">
      <Header />

      {/* Page Header */}
      <section className="py-12 text-white bg-primary-light">
        <div className="container-custom">
          <h1 className="mb-4">Inyandiko ngufi</h1>
          <p className="text-lg max-w-3xl">
            Sura inyandiko zacu zitandukanye zigaruka ku mahame ya Bibiliya, abagore, umuhamagaro wabo, n'inama ngiro z'ubuzima bufite intego.
          </p>
        </div>
      </section>

      {/* Search and Filter */}
      <section className="py-8 bg-secondary border-b border-secondary-dark">
        <div className="container-custom">
          <div className="flex flex-col lg:flex-row gap-4 justify-between items-start">
            {/* Search Bar */}
            <SearchBar
              onSearch={handleSearch}
              placeholder="Shakisha inyandiko..."
              initialValue={searchTerm}
              className="max-w-md w-full"
            />

            <div className="flex flex-col sm:flex-row gap-4 items-start">
              {/* Category Filter */}
              <CategoryFilter
                categories={categories}
                selectedCategories={selectedCategories}
                onCategoryChange={handleCategoryChange}
                className="min-w-[200px]"
              />

              {/* Author Filter */}
              <AuthorFilter
                selectedAuthor={selectedAuthor}
                onAuthorChange={handleAuthorChange}
                className="min-w-[200px]"
              />

              {/* Sort Options */}
              <select
                value={ordering}
                onChange={(e) => handleOrderingChange(e.target.value)}
                className="px-4 py-2 bg-background border border-secondary-dark rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent min-w-[200px]"
              >
                <option value={ORDERING_OPTIONS.NEWEST_FIRST}>Ibyashyizweho vuba</option>
                <option value={ORDERING_OPTIONS.OLDEST_FIRST}>Ibyashyizweho kera</option>
                <option value={ORDERING_OPTIONS.TITLE_ASC}>Umutwe w'inkuru A-Z</option>
                <option value={ORDERING_OPTIONS.TITLE_DESC}>Umutwe w'inkuru Z-A</option>
              </select>
            </div>
          </div>

          {/* Active filters display */}
          {(searchTerm || selectedCategories.length > 0 || selectedAuthor) && (
            <div className="mt-4 flex flex-wrap gap-2">
              {searchTerm && (
                <span className="inline-flex items-center gap-1 px-3 py-1 bg-primary text-white text-sm rounded-full">
                  Shakisha: "{searchTerm}"
                  <button onClick={() => handleSearch('')} className="ml-1 hover:text-red-200">×</button>
                </span>
              )}
              {selectedCategories.map((categorySlug) => {
                const category = categories.find(c => c.slug === categorySlug);
                return category ? (
                  <span key={categorySlug} className="inline-flex items-center gap-1 px-3 py-1 bg-accent text-white text-sm rounded-full">
                    {category.name}
                    <button
                      onClick={() => handleCategoryChange(selectedCategories.filter(c => c !== categorySlug))}
                      className="ml-1 hover:text-red-200"
                    >
                      ×
                    </button>
                  </span>
                ) : null;
              })}
              {selectedAuthor && (
                <span className="inline-flex items-center gap-1 px-3 py-1 bg-secondary text-white text-sm rounded-full">
                  Author Filter Active
                  <button onClick={() => handleAuthorChange(null)} className="ml-1 hover:text-red-200">×</button>
                </span>
              )}
            </div>
          )}
        </div>
      </section>

      {/* Articles Grid */}
      <section className="section bg-background">
        <div className="container-custom">
          {loading ? (
            <ArticleGridSkeleton count={9} />
          ) : error ? (
            <ErrorMessage
              message={error}
              onRetry={fetchArticles}
              className="max-w-md mx-auto"
            />
          ) : articles.length > 0 ? (
            <>
              <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
                {articles.map((article) => (
                  <ArticleCard key={article.id} article={article} />
                ))}
              </div>

              {/* Pagination */}
              {totalPages > 1 && (
                <Pagination
                  currentPage={currentPage}
                  totalPages={totalPages}
                  onPageChange={handlePageChange}
                  className="mt-12"
                />
              )}
            </>
          ) : (
            <div className="text-center py-12">
              <h3 className="text-xl font-semibold text-primary mb-2">Ntanyandiko ziraboneka ubu</h3>
              <p className="text-primary-dark">
                {searchTerm || selectedCategories.length > 0 || selectedAuthor
                  ? 'Gerageza guhindura ijambo ushakisha, inyandiko mu ibyiciro, cyangwa umwanditsi.'
                  : 'Uze kugenzura hanyuma ko hagize izishyirwaho'
                }
              </p>
            </div>
          )}
        </div>
      </section>

      <Footer />
    </div>
  );
}

// Main component with Suspense boundary
export default function ArticlesPage() {
  return (
    <Suspense fallback={
      <div>
        <Header />
        <div className="min-h-screen bg-background">
          <section className="section">
            <div className="container-custom">
              <div className="text-center mb-12">
                <h1 className="mb-4">Inyandiko ngufi</h1>
                <p className="text-lg text-primary-dark max-w-2xl mx-auto">
                  Sura inyandiko zacu zitandukanye zigaruka ku mahame ya Bibiliya, abagore, umuhamagaro wabo, n'inama ngiro z'ubuzima bufite intego.
                </p>
              </div>
              <ArticleGridSkeleton count={9} />
            </div>
          </section>
        </div>
        <Footer />
      </div>
    }>
      <ArticlesContent />
    </Suspense>
  );
}
