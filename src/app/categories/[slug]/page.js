'use client';

import { useState, useEffect } from 'react';
import { useParams } from 'next/navigation';
import Header from '../../../components/Header';
import Footer from '../../../components/Footer';
import ArticleCard from '../../../components/ArticleCard';
import SearchBar from '../../../components/SearchBar';
import Pagination from '../../../components/Pagination';
import LoadingSpinner from '../../../components/LoadingSpinner';
import ErrorMessage from '../../../components/ErrorMessage';
import { ArticleGridSkeleton } from '../../../components/SkeletonCard';
import { api } from '../../../lib/api';
import { ORDERING_OPTIONS } from '../../../lib/constants';
import Link from 'next/link';
import { notFound } from 'next/navigation';

export default function CategoryPage() {
  const params = useParams();
  const { slug } = params;

  const [category, setCategory] = useState(null);
  const [articles, setArticles] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [searchTerm, setSearchTerm] = useState('');
  const [ordering, setOrdering] = useState(ORDERING_OPTIONS.NEWEST_FIRST);

  // Fetch category details
  const fetchCategory = async () => {
    try {
      const categoryData = await api.categories.getBySlug(slug);
      setCategory(categoryData);
    } catch (err) {
      console.error('Error fetching category:', err);
      // Check if it's a 404 error (category doesn't exist)
      if (err.message.includes('404')) {
        setCategory(null);
      } else {
        // For other errors, set an error state but don't trigger 404
        setError(`Failed to load category: ${err.message}`);
      }
    }
  };

  // Fetch articles for this category
  const fetchArticles = async () => {
    setLoading(true);
    setError(null);

    try {
      const params = {
        page: currentPage,
        pageSize: 9,
        categoryName: category?.name || slug,
        search: searchTerm,
        ordering: ordering,
      };

      const response = await api.articles.getAll(params);
      setArticles(response.results || []);
      setTotalPages(response.total_pages || 1);
    } catch (err) {
      setError(err.message);
      setArticles([]);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchCategory();
  }, [slug]);

  useEffect(() => {
    fetchArticles();
  }, [currentPage, searchTerm, ordering, slug]);

  // Event handlers
  const handleSearch = (term) => {
    setSearchTerm(term);
    setCurrentPage(1);
  };

  const handlePageChange = (page) => {
    setCurrentPage(page);
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  const handleOrderingChange = (newOrdering) => {
    setOrdering(newOrdering);
    setCurrentPage(1);
  };

  // Only show 404 if category is null and we're not loading and there's no other error
  if (category === null && !loading && !error) {
    notFound();
  }

  return (
    <div className="min-h-screen flex flex-col">
      <Header />

      {/* Page Header */}
      <section className="bg-primary py-12 text-white">
        <div className="container-custom">
          <Link href="/categories" className="inline-block mb-4 hover:underline">
            ← Back to Categories
          </Link>
          <h1 className="mb-4">{category?.name || 'Category'}</h1>
          {category?.description && (
            <p className="text-lg max-w-3xl">
              {category.description}
            </p>
          )}
          {error && !category && (
            <div className="bg-red-500 bg-opacity-20 border border-red-300 rounded-lg p-4 mt-4">
              <p className="text-red-100">{error}</p>
            </div>
          )}
        </div>
      </section>

      {/* Search and Filter */}
      <section className="py-8 bg-neutral-100 border-b border-neutral-200">
        <div className="container-custom">
          <div className="flex flex-col lg:flex-row gap-4 justify-between items-start">
            {/* Search Bar */}
            <SearchBar
              onSearch={handleSearch}
              placeholder="Search articles in this category..."
              className="max-w-md w-full"
            />

            {/* Sort Options */}
            <select
              value={ordering}
              onChange={(e) => handleOrderingChange(e.target.value)}
              className="px-4 py-2 bg-white border border-neutral-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
            >
              <option value={ORDERING_OPTIONS.NEWEST_FIRST}>Newest First</option>
              <option value={ORDERING_OPTIONS.OLDEST_FIRST}>Oldest First</option>
              <option value={ORDERING_OPTIONS.TITLE_ASC}>Title A-Z</option>
              <option value={ORDERING_OPTIONS.TITLE_DESC}>Title Z-A</option>
            </select>
          </div>

          {/* Active search display */}
          {searchTerm && (
            <div className="mt-4">
              <span className="inline-flex items-center gap-1 px-3 py-1 bg-primary text-white text-sm rounded-full">
                Search: "{searchTerm}"
                <button onClick={() => handleSearch('')} className="ml-1 hover:text-red-200">×</button>
              </span>
            </div>
          )}
        </div>
      </section>

      {/* Articles Grid */}
      <section className="section bg-neutral-100">
        <div className="container-custom">
          {loading ? (
            <ArticleGridSkeleton count={9} />
          ) : error ? (
            <ErrorMessage
              message={error}
              onRetry={fetchArticles}
              className="max-w-md mx-auto"
            />
          ) : articles.length > 0 ? (
            <>
              <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
                {articles.map((article) => (
                  <ArticleCard key={article.id} article={article} />
                ))}
              </div>

              {/* Pagination */}
              {totalPages > 1 && (
                <Pagination
                  currentPage={currentPage}
                  totalPages={totalPages}
                  onPageChange={handlePageChange}
                  className="mt-12"
                />
              )}
            </>
          ) : (
            <div className="text-center py-12">
              <h3 className="text-xl font-semibold text-neutral-600 mb-2">No articles found</h3>
              <p className="text-neutral-500">
                {searchTerm
                  ? 'Try adjusting your search term.'
                  : 'No articles have been published in this category yet.'
                }
              </p>
            </div>
          )}
        </div>
      </section>

      <Footer />
    </div>
  );
}
