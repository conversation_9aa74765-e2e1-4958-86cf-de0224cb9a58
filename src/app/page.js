'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import Header from '@/components/Header';
import Footer from '@/components/Footer';
import ArticleCard from '@/components/ArticleCard';
import TestimonialCard from '@/components/TestimonialCard';
import NewsletterForm from '@/components/NewsletterForm';
import PartnerCard from '@/components/PartnerCard';
import LoadingSpinner from '@/components/LoadingSpinner';
import ErrorMessage from '@/components/ErrorMessage';
import { ArticleGridSkeleton, TestimonialCardSkeleton, PartnerGridSkeleton } from '@/components/SkeletonCard';
import { FaArrowRight, FaChevronLeft, FaChevronRight } from 'react-icons/fa';
import { api } from '@/lib/api';
import cache, { cacheKeys } from '@/lib/cache';

export default function Home() {
  const router = useRouter();
  const [data, setData] = useState({
    articles: [],
    testimonies: [],
    partners: [],
  });
  const [loading, setLoading] = useState({
    articles: true,
    testimonies: true,
    partners: true,
  });
  const [error, setError] = useState(null);
  const [currentTestimonialIndex, setCurrentTestimonialIndex] = useState(0);

  // Handler functions for hero buttons
  const handleExploreArticles = () => {
    router.push('/articles');
  };

  const handleJoinCommunity = () => {
    // Scroll to newsletter section
    const newsletterSection = document.getElementById('newsletter-section');
    if (newsletterSection) {
      newsletterSection.scrollIntoView({ behavior: 'smooth' });
    } else {
      // Fallback to subscribe page
      router.push('/subscribe');
    }
  };

  // Testimonial slider navigation
  const nextTestimonial = () => {
    setCurrentTestimonialIndex((prev) =>
      prev === data.testimonies.length - 1 ? 0 : prev + 1
    );
  };

  const prevTestimonial = () => {
    setCurrentTestimonialIndex((prev) =>
      prev === 0 ? data.testimonies.length - 1 : prev - 1
    );
  };

  useEffect(() => {
    const fetchHomePageData = async () => {
      // Check cache first
      const cacheKey = cacheKeys.homepage();
      const cachedData = cache.get(cacheKey);

      if (cachedData) {
        setData(cachedData);
        setLoading({ articles: false, testimonies: false, partners: false });
        return;
      }

      try {
        // Set a timeout to prevent infinite loading
        const timeoutId = setTimeout(() => {
          setError('Request timed out. Please check your internet connection.');
          setLoading({ articles: false, testimonies: false, partners: false });
        }, 30000); // 30 seconds timeout

        // Fetch all data with individual error handling
        const [articlesResponse, testimoniesResponse, partnersResponse] = await Promise.allSettled([
          api.articles.getAll({ pageSize: 3 }),
          api.testimonies.getAll({ pageSize: 3 }),
          api.partners.getAll({ pageSize: 4 })
        ]);

        clearTimeout(timeoutId);

        const finalData = {
          articles: articlesResponse.status === 'fulfilled' ? (articlesResponse.value.results || []) : [],
          testimonies: testimoniesResponse.status === 'fulfilled' ? (testimoniesResponse.value.results || []) : [],
          partners: partnersResponse.status === 'fulfilled' ? (partnersResponse.value.results || []) : [],
        };

        setData(finalData);
        setLoading({ articles: false, testimonies: false, partners: false });

        // Cache the complete data for 5 minutes
        cache.set(cacheKey, finalData, 300000);

        // Log any failed requests
        if (articlesResponse.status === 'rejected') {
          console.error('Failed to fetch articles:', articlesResponse.reason);
        }
        if (testimoniesResponse.status === 'rejected') {
          console.error('Failed to fetch testimonies:', testimoniesResponse.reason);
        }
        if (partnersResponse.status === 'rejected') {
          console.error('Failed to fetch partners:', partnersResponse.reason);
        }

      } catch (err) {
        console.error('Error fetching homepage data:', err);
        setError(err.message || 'Failed to load data. Please try again later.');
        setLoading({ articles: false, testimonies: false, partners: false });
      }
    };

    fetchHomePageData();
  }, []);
  return (
    <div className="min-h-screen flex flex-col">
      <Header />

      {/* Hero Section */}
      <section className="py-16 md:py-24 text-white bg-primary-light">
        <div className="container-custom grid md:grid-cols-2 gap-8 items-center">
          <div>
            <h1 className="mb-4">Umugore w'ubaha Uwiteka niwe Uzashimwa</h1>
            <p className="text-lg mb-6">
              Ikaze k'urubuga umugore uzashimwa minitry. Inyadiko zikubiye hano kurubuga,
              zijye zitubera nk'icyapa, cyidutungira agatoki mu Ibyanditswe byera (Bibiliya). Ntibizasim-
              bure urukundo wakundaga gusoma Ibyanditswe byera, ntibizakurangaze, ahubwo bizagutera umuhate wo gukunda no gusoma ibyanditswe byera kurushaho.
            </p>
            <div className="flex flex-col sm:flex-row gap-4">
              <button
                onClick={handleExploreArticles}
                className="btn bg-background text-primary hover:bg-secondary transition-colors"
              >
                Sura Inyandiko
              </button>
              <button
                onClick={handleJoinCommunity}
                className="btn bg-accent text-white hover:bg-accent-dark transition-colors"
              >
                Jya mu Muryango
              </button>
            </div>
          </div>
          <div className="relative h-80 md:h-96">
            <div className="absolute inset-0 bg-secondary rounded-lg overflow-hidden">
              <div className="w-full h-full flex items-center justify-center text-primary">
                Hero Image Placeholder
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Latest Articles Section */}
      <section className="section bg-secondary">
        <div className="container-custom">
          <div className="flex justify-between items-center mb-8">
            <h2>Inyandiko nshya</h2>
            <a href="/articles" className="flex items-center gap-2 text-primary font-medium">
              Reba zose <FaArrowRight className="text-sm" />
            </a>
          </div>

          {loading.articles ? (
            <ArticleGridSkeleton count={3} />
          ) : error ? (
            <ErrorMessage message="Failed to load articles. Please try again later." />
          ) : data.articles.length > 0 ? (
            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
              {data.articles.map((article) => (
                <ArticleCard key={article.id} article={article} />
              ))}
            </div>
          ) : (
            <div className="text-center py-12">
              <h3 className="text-xl font-semibold text-primary mb-2">Ntanyandiko ziraboneka ubu</h3>
              <p className="text-primary-dark">Uze kugenzura hanyuma ko hagize izishyirwaho</p>
            </div>
          )}
        </div>
      </section>

      {/* Testimonials Section */}
      <section className="section bg-secondary-light">
        <div className="container-custom">
          <h2 className="text-center mb-12">Ubuhamya bushimishije</h2>
          <div className="max-w-4xl mx-auto">
            {loading.testimonies ? (
              <TestimonialCardSkeleton />
            ) : data.testimonies.length > 0 ? (
              <div className="relative">
                <TestimonialCard testimonial={data.testimonies[currentTestimonialIndex]} />

                {data.testimonies.length > 1 && (
                  <>
                    {/* Navigation Buttons */}
                    <button
                      onClick={prevTestimonial}
                      className="absolute left-0 top-1/2 transform -translate-y-1/2 -translate-x-4 bg-background rounded-full p-3 shadow-lg hover:shadow-xl transition-shadow text-primary hover:text-primary-dark"
                      aria-label="Previous testimony"
                    >
                      <FaChevronLeft className="text-lg" />
                    </button>
                    <button
                      onClick={nextTestimonial}
                      className="absolute right-0 top-1/2 transform -translate-y-1/2 translate-x-4 bg-background rounded-full p-3 shadow-lg hover:shadow-xl transition-shadow text-primary hover:text-primary-dark"
                      aria-label="Next testimony"
                    >
                      <FaChevronRight className="text-lg" />
                    </button>

                    {/* Dots Indicator */}
                    <div className="flex justify-center mt-6 space-x-2">
                      {data.testimonies.map((_, index) => (
                        <button
                          key={index}
                          onClick={() => setCurrentTestimonialIndex(index)}
                          className={`w-3 h-3 rounded-full transition-colors ${
                            index === currentTestimonialIndex
                              ? 'bg-primary'
                              : 'bg-secondary-dark hover:bg-primary-light'
                          }`}
                          aria-label={`Go to testimony ${index + 1}`}
                        />
                      ))}
                    </div>
                  </>
                )}
              </div>
            ) : (
              <div className="text-center py-12">
                <h3 className="text-xl font-semibold text-primary mb-2">Nta buhamya buraboneka ubu</h3>
                <p className="text-primary-dark">Waba wa mbere gusangira inkuru yawe ishimishije!</p>
              </div>
            )}
          </div>
        </div>
      </section>

      {/* Partners Section */}
      <section className="section bg-background">
        <div className="container-custom">
          <div className="flex flex-col md:flex-row md:justify-between md:items-center mb-8 md:mb-12">
            <h2 className="text-center md:text-left mb-4 md:mb-0 md:flex-1">Abafatanya bikorwa bacu</h2>
            <a href="/partners" className="flex items-center justify-center md:justify-start gap-2 text-primary font-medium text-sm md:text-base">
              Reba abafatanya bikorwa bose <FaArrowRight className="text-sm" />
            </a>
          </div>
          {loading.partners ? (
            <PartnerGridSkeleton count={4} />
          ) : data.partners.length > 0 ? (
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 md:gap-8">
              {data.partners.map((partner) => (
                <PartnerCard key={partner.id} partner={partner} />
              ))}
            </div>
          ) : (
            <div className="text-center py-12">
              <h3 className="text-xl font-semibold text-primary mb-2">Nta bafatanya bikorwa baraboneka ubu</h3>
              <p className="text-primary-dark">Turimo kubaka ubufatanye bwo gukorera neza umuryango wacu.</p>
            </div>
          )}
        </div>
      </section>

      {/* Newsletter Section */}
      <section id="newsletter-section" className="section text-white bg-primary-light">
        <div className="container-custom max-w-4xl mx-auto text-center">
          <h2 className="mb-4">Habwa amakuru yacu</h2>
          <p className="mb-8">Iyandikishe maze ujye ubasha guhabwa inyandiko nshya, umenyeshwe gahunda za minisiteri mugihe zihari.</p>
          <NewsletterForm />
        </div>
      </section>

      <Footer />
    </div>
  );
}
